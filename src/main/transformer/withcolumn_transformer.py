from config.schemas.transformer import WithColumnTransformerConfig
from pyspark.sql.dataframe import DataFrame
from pyspark.sql.functions import expr
from transformer.transformer import Transformer


class WithColumnTransformer(Transformer):
    def __init__(self, config: WithColumnTransformerConfig):
        self.config = config

    def transform(self, data: DataFrame):
        for c in self.config.columns:
            data = data.withColumn(c.colname, expr(c.expr))
        return data
