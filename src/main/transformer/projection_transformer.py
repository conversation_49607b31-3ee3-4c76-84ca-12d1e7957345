from config.schemas.transformer import ProjectionTransformerConfig
from pyspark.sql.dataframe import DataFrame
from transformer.transformer import Transformer


class ProjectionTransformer(Transformer):
    def __init__(self, config: ProjectionTransformerConfig):
        self.config = config

    def transform(self, data: DataFrame):
        list_col = [col for col in self.config.columns]
        data = data.select(*list_col)
        return data
