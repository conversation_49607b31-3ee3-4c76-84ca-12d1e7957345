"""
AWS Configuration Management
Handles loading AWS credentials and configuration from files and environment variables
"""

import os
import logging
from typing import Optional
from config.schemas.endpoint import AwsConfig, AwsConfigWrapper, S3Config
from config.schemas.configparser import load_yaml
from utils import file_util

logger = logging.getLogger(__name__)


class AwsConfigManager:
    """Manages AWS configuration loading and credential resolution"""

    def __init__(self, config_root_dir: Optional[str] = None):
        self.config_root_dir = config_root_dir or os.getenv(
            'CONFIG_ROOT_DIR', 'src/main/config')
        self.aws_config_dir = os.path.join(self.config_root_dir, 'data', 'aws')

    def load_aws_config(self, environment: str = 'dev') -> AwsConfig:
        """
        Load AWS configuration for the specified environment

        Args:
            environment: Environment name (dev, prod, staging, etc.)

        Returns:
            AwsConfig: Loaded and resolved AWS configuration
        """
        config_file = f"aws.{environment}.yaml"
        config_path = os.path.join(self.aws_config_dir, config_file)

        if not os.path.exists(config_path):
            logger.warning(f"AWS config file not found: {config_path}")
            return self._create_default_config()

        try:
            # Load configuration from file
            config_content = file_util.read_file(config_path)
            config_wrapper = load_yaml(config_content, AwsConfigWrapper)
            aws_config = config_wrapper.aws_config

            # Resolve environment variables and apply overrides
            resolved_config = self._resolve_environment_variables(aws_config)

            logger.info(
                f"Loaded AWS configuration for environment: {environment}")
            return resolved_config

        except Exception as e:
            logger.error(f"Failed to load AWS config from {config_path}: {e}")
            return self._create_default_config()

    def _resolve_environment_variables(self, config: AwsConfig) -> AwsConfig:
        """
        Resolve environment variables in the configuration
        Environment variables take precedence over file values
        """
        # Create a copy to avoid modifying the original
        resolved_config = config.model_copy()

        # Override with environment variables if they exist
        if os.getenv('AWS_ACCESS_KEY_ID'):
            resolved_config.access_key_id = os.getenv('AWS_ACCESS_KEY_ID')

        if os.getenv('AWS_SECRET_ACCESS_KEY'):
            resolved_config.secret_access_key = os.getenv(
                'AWS_SECRET_ACCESS_KEY')

        if os.getenv('AWS_SESSION_TOKEN'):
            resolved_config.session_token = os.getenv('AWS_SESSION_TOKEN')

        if os.getenv('AWS_DEFAULT_REGION'):
            resolved_config.region = os.getenv('AWS_DEFAULT_REGION')

        # S3 endpoint override
        if os.getenv('S3_ENDPOINT'):
            if not resolved_config.s3:
                resolved_config.s3 = S3Config()
            resolved_config.s3.endpoint_url = os.getenv('S3_ENDPOINT')

        return resolved_config

    def _create_default_config(self) -> AwsConfig:
        """Create a default AWS configuration from environment variables"""
        return AwsConfig(
            access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
            session_token=os.getenv('AWS_SESSION_TOKEN'),
            region=os.getenv('AWS_DEFAULT_REGION', 'us-east-1'),
            s3=S3Config(
                endpoint_url=os.getenv('S3_ENDPOINT'),
                path_style_access=os.getenv(
                    'S3_PATH_STYLE_ACCESS', 'false').lower() == 'true'
            ),
            environment='default'
        )

    def get_spark_s3_config(self, aws_config: AwsConfig) -> dict:
        """
        Convert AWS configuration to Spark S3 configuration dictionary

        Args:
            aws_config: AWS configuration

        Returns:
            dict: Spark configuration for S3 access
        """
        config = {}

        # Basic S3A configuration
        config["spark.hadoop.fs.s3a.impl"] = "org.apache.hadoop.fs.s3a.S3AFileSystem"

        # Credentials
        if aws_config.access_key_id and aws_config.secret_access_key:
            config["spark.hadoop.fs.s3a.access.key"] = aws_config.access_key_id
            config["spark.hadoop.fs.s3a.secret.key"] = aws_config.secret_access_key

            if aws_config.session_token:
                config["spark.hadoop.fs.s3a.session.token"] = aws_config.session_token
                config["spark.hadoop.fs.s3a.aws.credentials.provider"] = "org.apache.hadoop.fs.s3a.TemporaryAWSCredentialsProvider"
            else:
                config["spark.hadoop.fs.s3a.aws.credentials.provider"] = "org.apache.hadoop.fs.s3a.SimpleAWSCredentialsProvider"
        elif aws_config.use_iam_role:
            config["spark.hadoop.fs.s3a.aws.credentials.provider"] = "com.amazonaws.auth.InstanceProfileCredentialsProvider"

        # S3 endpoint configuration
        if aws_config.s3 and aws_config.s3.endpoint_url:
            config["spark.hadoop.fs.s3a.endpoint"] = aws_config.s3.endpoint_url
            config["spark.hadoop.fs.s3a.path.style.access"] = str(
                aws_config.s3.path_style_access).lower()

        return config


# Global instance for easy access
aws_config_manager = AwsConfigManager()
