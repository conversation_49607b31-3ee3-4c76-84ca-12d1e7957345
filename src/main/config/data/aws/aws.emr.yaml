# AWS Configuration for EMR Environment
# This configuration uses IAM roles - no hardcoded credentials needed

aws_config:
  # AWS Credentials - Use IAM roles on EMR (recommended)
  access_key_id: null  # Will use IAM role
  secret_access_key: null  # Will use IAM role
  session_token: null  # Will use IAM role
  
  # AWS Region - set via environment variable
  region: ${AWS_DEFAULT_REGION:us-east-1}
  
  # S3 Configuration for production AWS
  s3:
    endpoint_url: null  # Use default AWS S3 endpoint
    path_style_access: false  # Use virtual-hosted-style for AWS S3
    
  # Environment type
  environment: emr
  
  # Security settings
  use_ssl: true
  verify_ssl: true
  
  # IAM Role configuration (recommended for EMR)
  use_iam_role: true
  iam_role_arn: ${EMR_EXECUTION_ROLE_ARN}  # Optional: specific role ARN
