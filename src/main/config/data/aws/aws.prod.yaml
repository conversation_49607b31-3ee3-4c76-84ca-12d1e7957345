# AWS Configuration for Production Environment
# SECURITY WARNING: DO NOT put real AWS credentials in this file!
# Use environment variables or AWS IAM roles for production

aws_config:
  # AWS Credentials - USE ENVIRONMENT VARIABLES OR IAM ROLES IN PRODUCTION
  # These should be set via environment variables:
  # - AWS_ACCESS_KEY_ID
  # - AWS_SECRET_ACCESS_KEY  
  # - AWS_SESSION_TOKEN (optional)
  access_key_id: ${AWS_ACCESS_KEY_ID}
  secret_access_key: ${AWS_SECRET_ACCESS_KEY}
  session_token: ${AWS_SESSION_TOKEN}  # Optional
  
  # AWS Region
  region: ${AWS_DEFAULT_REGION:us-east-1}
  
  # S3 Configuration
  s3:
    endpoint_url: null  # Use default AWS S3 endpoint
    path_style_access: false  # Use virtual-hosted-style for AWS S3
    
  # Environment type
  environment: production
  
  # Security settings
  use_ssl: true
  verify_ssl: true
  
  # IAM Role (preferred for production)
  use_iam_role: true
  iam_role_arn: ${AWS_ROLE_ARN}  # Optional: specific role to assume
