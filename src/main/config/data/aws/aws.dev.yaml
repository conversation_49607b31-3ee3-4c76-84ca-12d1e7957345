# AWS Configuration for Development Environment (LocalStack)
# WARNING: This file contains sensitive credentials - DO NOT commit real AWS keys to version control
# For production, use environment variables or AWS IAM roles

aws_config:
  # AWS Credentials (for LocalStack development only)
  access_key_id: test
  secret_access_key: test
  session_token: test  # Optional, for temporary credentials
  
  # AWS Region
  region: us-east-1
  
  # S3 Configuration
  s3:
    endpoint_url: http://localstack:4566  # LocalStack endpoint
    path_style_access: true  # Required for LocalStack
    
  # Environment type
  environment: development
  
  # Security settings
  use_ssl: false  # LocalStack doesn't use SSL
  verify_ssl: false  # LocalStack doesn't use SSL
