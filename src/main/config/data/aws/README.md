# AWS Configuration

This directory contains AWS configuration files for different environments.

## Security Warning ⚠️

**NEVER commit real AWS credentials to version control!**

## Configuration Files

### Development Environment
- `aws.dev.yaml` - Contains dummy credentials for LocalStack development
- Safe to commit to version control

### Production Environment
- `aws.prod.yaml` - Template for production configuration
- **DO NOT** put real credentials in this file
- Use environment variables or AWS IAM roles instead

## Environment Variables

For production and staging environments, use these environment variables:

```bash
# Required AWS credentials
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret-key"
export AWS_SESSION_TOKEN="your-session-token"  # Optional, for temporary credentials

# AWS region
export AWS_DEFAULT_REGION="us-east-1"

# S3 endpoint (for LocalStack or custom S3)
export S3_ENDPOINT="http://localhost:4566"  # LocalStack only

# Environment selection
export AWS_ENVIRONMENT="prod"  # dev, prod, staging, etc.
```

## Usage

The AWS configuration is automatically loaded based on the `AWS_ENVIRONMENT` environment variable:

```python
from config.aws_config import aws_config_manager

# Load configuration for current environment
aws_config = aws_config_manager.load_aws_config()

# Get Spark S3 configuration
spark_config = aws_config_manager.get_spark_s3_config(aws_config)
```

## Best Practices

1. **Development**: Use `aws.dev.yaml` with dummy credentials for LocalStack
2. **Production**: Use environment variables or AWS IAM roles
3. **Never**: Commit real AWS credentials to version control
4. **Always**: Use IAM roles when running on AWS infrastructure

## IAM Roles (Recommended for Production)

For production deployments on AWS, use IAM roles instead of access keys:

```yaml
aws_config:
  use_iam_role: true
  iam_role_arn: "arn:aws:iam::123456789012:role/MySparkRole"  # Optional
```

## File Structure

```
aws/
├── README.md           # This file
├── aws.dev.yaml        # Development configuration (safe to commit)
└── aws.prod.yaml       # Production template (DO NOT put real credentials)
```
