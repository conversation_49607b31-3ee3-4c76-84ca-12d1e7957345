# EMR Job Configuration
# Production job configuration for AWS EMR

jobs:
  - name: mysql_to_s3_emr
    readers:
      type: MysqlReader
      endpoint: mysql.prod.endpoint.yaml  # Production MySQL endpoint
      table: customers
      incremental_column: updated_at
      batch_size: 10000  # Larger batch for production
    transformers:
      - type: WithColumnTransformer
        columns:
          - colname: age_range
            expr: CASE WHEN age between 20 and 40 THEN 'young' ELSE 'mature' END
          - colname: processing_date
            expr: current_date()
    writers:
      type: IcebergWriter
      endpoint: s3.prod.endpoint.yaml  # Production S3 endpoint
      s3_path: data-lake/customers/mysql
      partition_columns:
        - age_range
        - processing_date
      
  - name: mssql_to_s3_emr
    readers:
      type: MssqlReader
      endpoint: mssql.prod.endpoint.yaml  # Production SQL Server endpoint
      table: customers
      incremental_column: updated_at
      batch_size: 10000  # Larger batch for production
    transformers:
      - type: WithColumnTransformer
        columns:
          - colname: age_range
            expr: CASE WHEN age between 20 and 40 THEN 'young' ELSE 'mature' END
          - colname: processing_date
            expr: current_date()
    writers:
      type: IcebergWriter
      endpoint: s3.prod.endpoint.yaml  # Production S3 endpoint
      s3_path: data-lake/customers/mssql
      partition_columns:
        - age_range
        - processing_date
