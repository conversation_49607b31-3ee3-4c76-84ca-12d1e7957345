jobs:
  - name: job1
    readers:
      type: MysqlReader
      endpoint: mysql.endpoint.yaml
      table: customers
      strategy: delta
      delta_column: updated_at
    transformers:
       - type: WithColumnTransformer
         columns:
           - colname: range
             expr: CASE WHEN age between 20 and 40 THEN 1 ELSE 2 END
       - type: ProjectionTransformer
         columns: 
           - id
           - name  
           - age
           - range
    writers:
      type: PostgresWriter
      endpoint: postgresql.endpoint.yaml
      mode: append
      table: mysql_customers
  - name: job2
    readers:
      type: MssqlReader
      endpoint: mssql.endpoint.yaml
      table: customers
      strategy: delta
      delta_column: updated_at
    transformers:
       - type: WithColumnTransformer
         columns:
           - colname: range
             expr: CASE WHEN age between 20 and 40 THEN 1 ELSE 2 END
       - type: ProjectionTransformer
         columns: 
           - id
           - name  
           - age
           - range
    writers:
      type: PostgresWriter
      endpoint: postgresql.endpoint.yaml
      mode: append
      table: mssql_customers  

  - name: job3
    readers:
      type: MssqlReader
      endpoint: mssql.endpoint.yaml
      table: customers
      strategy: full
    transformers:
       - type: WithColumnTransformer
         columns:
           - colname: range
             expr: CASE WHEN age between 20 and 40 THEN 1 ELSE 2 END
       - type: ProjectionTransformer
         columns: 
           - id
           - name  
           - age
           - range
    writers:
      type: S3Writer
      endpoint: s3.localstack.endpoint.yaml
      key: outbound/json_dummy
      mode: overwrite
      num_files: 1
      format: 
        type: JsonWriterFormat
        source_options:
          inferSchema: 'true'

  - name: job4
    readers:
      type: S3Reader
      endpoint: s3.localstack.endpoint.yaml
      key: outbound/json_dummy
      format: 
        type: JsonReaderFormat
        source_options:
          inferSchema: 'true'
    transformers:
       - type: ProjectionTransformer
         columns: 
           - id
           - name  
           - age
           - range
    writers:
      type: PostgresWriter
      endpoint: postgresql.endpoint.yaml
      mode: append
      table: s3_customers
  