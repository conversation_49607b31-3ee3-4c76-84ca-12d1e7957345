"""
Reusable Spark Session Builder for Iceberg Integration
Centralizes Spark configuration to avoid hardcoding in scripts
"""

from pyspark.sql import SparkSession
from config.environment import env_config
import os


class SparkSessionBuilder:
    """Builder class for creating Spark sessions with proper Iceberg configuration"""
    
    def __init__(self, app_name: str = "IcebergApp"):
        self.app_name = app_name
        self.builder = SparkSession.builder.appName(app_name)
        self._configure_base_settings()
    
    def _configure_base_settings(self):
        """Configure base Spark settings from environment config"""
        spark_config = env_config.get_spark_config()
        
        for key, value in spark_config.items():
            self.builder = self.builder.config(key, value)
    
    def with_log_level(self, level: str = "WARN"):
        """Set Spark log level after session creation"""
        self.log_level = level
        return self
    
    def build(self) -> SparkSession:
        """Build and return configured Spark session"""
        spark = self.builder.getOrCreate()
        
        # Set log level if specified
        if hasattr(self, 'log_level'):
            spark.sparkContext.setLogLevel(self.log_level)
        
        return spark
    
    @staticmethod
    def create_iceberg_session(app_name: str = "IcebergApp", log_level: str = "WARN") -> SparkSession:
        """Convenience method to create a properly configured Iceberg Spark session"""
        return (SparkSessionBuilder(app_name)
                .with_log_level(log_level)
                .build())


def get_environment_variables() -> dict:
    """Get required environment variables for Iceberg operations"""
    return {
        'CONFIG_ROOT_DIR': os.getenv('CONFIG_ROOT_DIR', 'src/main/resources'),
        'CONFIG_ENDPOINTS_DIR': os.getenv('CONFIG_ENDPOINTS_DIR', 'src/main/resources/endpoints'),
        'CONFIG_JOBS_DIR': os.getenv('CONFIG_JOBS_DIR', 'src/main/resources/jobs'),
        'EXTRA_JARS_FOLDER': os.getenv('EXTRA_JARS_FOLDER', 'src/main/resources/jars'),
        'AWS_ACCESS_KEY_ID': os.getenv('AWS_ACCESS_KEY_ID', 'test'),
        'AWS_SECRET_ACCESS_KEY': os.getenv('AWS_SECRET_ACCESS_KEY', 'test'),
        'AWS_DEFAULT_REGION': os.getenv('AWS_DEFAULT_REGION', 'us-east-1'),
        'S3_ENDPOINT': os.getenv('S3_ENDPOINT', 'http://localhost:4566')
    }


def setup_environment():
    """Setup environment variables for Iceberg operations"""
    env_vars = get_environment_variables()
    for key, value in env_vars.items():
        os.environ[key] = value
