from typing import Optional, Literal, Union
from .endpoint import *
from .configparser import load_yaml
from utils import file_util
from pydantic import BaseModel
import os


class WriterFormatConfig(BaseModel):
    type: Literal["DSVWriterFormat", "ParquetWriterFormat", "AvroWriterFormat", "XMLWriterFormat",
                  "ORCWriterFormat", "ExcelWriterFormat", "JsonWriterFormat", "IcebergWriterFormat"]
    source_options: Optional[dict[str, str]]


class DSVWriterFormatConfig(WriterFormatConfig):
    type: Literal["DSVWriterFormat"]  # type: ignore
    source_options: Optional[dict[str, str]]


class ParquetWriterFormatConfig(WriterFormatConfig):
    type: Literal["ParquetWriterFormat"]  # type: ignore
    source_options: Optional[dict[str, str]]


class AvroWriterFormatConfig(WriterFormatConfig):
    type: Literal["AvroWriterFormat"]  # type: ignore
    source_options: Optional[dict[str, str]]


class XMLWriterFormatConfig(WriterFormatConfig):
    type: Literal["XMLWriterFormat"]  # type: ignore
    source_options: Optional[dict[str, str]]


class ORCWriterFormatConfig(WriterFormatConfig):
    type: Literal["ORCWriterFormat"]  # type: ignore
    source_options: Optional[dict[str, str]]


class ExcelWriterFormatConfig(WriterFormatConfig):
    type: Literal["ExcelWriterFormat"]  # type: ignore
    source_options: Optional[dict[str, str]]


class JsonlWriterFormatConfig(WriterFormatConfig):
    type: Literal["JsonWriterFormat"]  # type: ignore
    source_options: Optional[dict[str, str]]


class IcebergWriterFormatConfig(WriterFormatConfig):
    type: Literal["IcebergWriterFormat"]  # type: ignore
    source_options: Optional[dict[str, str]]


class WriterConfig(BaseModel):
    type: Literal["PostgresWriter", "S3Writer", "IcebergWriter"]
    endpoint: str = ""
    endpoint_config: Optional[BaseEndpoint] = None

    def __init__(self, **data):
        super().__init__(**data)

        # Mapping of reader types to endpoint models
        endpoint_mapping = {
            "PostgresWriter": PostgresEndpoint,
            "S3Writer": S3Endpoint,
            "IcebergWriter": S3Endpoint  # Iceberg uses S3 endpoint for storage
        }

        model = endpoint_mapping.get(self.type)
        if model is None:
            raise ValueError(f"Unknown reader type: {self.type}")
        endpoint_root_dir = os.getenv("CONFIG_ENDPOINTS_DIR")
        # Initialize `endpoint_config` by parsing the YAML file
        self.endpoint_config = load_yaml(file_util.read_file(
            f"{endpoint_root_dir}/{self.endpoint}"), model)


class PostgresWriterConfig(WriterConfig):
    type: Literal["PostgresWriter"]  # type: ignore
    table: str
    mode: str
    ssl_mode: str = "disable"


WRITER_FORMAT_UNION = Union[DSVWriterFormatConfig, ParquetWriterFormatConfig, AvroWriterFormatConfig,
                            XMLWriterFormatConfig, ORCWriterFormatConfig, ExcelWriterFormatConfig, JsonlWriterFormatConfig, IcebergWriterFormatConfig]


class S3WriterConfig(WriterConfig):
    type: Literal["S3Writer"]  # type: ignore
    key: str
    mode: str
    num_files: int
    partition_columns: Optional[list[str]] = None
    format: WRITER_FORMAT_UNION
    schema_: Optional[str] = None


class IcebergWriterConfig(WriterConfig):
    type: Literal["IcebergWriter"]  # type: ignore
    s3_path: Optional[str] = None  # Simple S3 path like "mysql_customers"
    database: str = "default"  # Fallback if s3_path not provided
    table: str = "data"  # Fallback if s3_path not provided
    mode: str = "append"  # append, overwrite
    partition_columns: Optional[list[str]] = None
    format: IcebergWriterFormatConfig
