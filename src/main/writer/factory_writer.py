from config.schemas.writer import PostgresWriterConfig, S3WriterConfig, IcebergWriterConfig
from writer.writer import Writer
from writer.postgre_writer import PostgreWriter
from writer.s3_writer import S3Writer
from writer.iceberg_writer import IcebergWriter
from typing import Optional


class WriterFactory:
    @staticmethod
    def create(config: Writer, process_date: str, custom_options: Optional[dict[str, str]]) -> Writer:
        if isinstance(config, PostgresWriterConfig):
            writer = PostgreWriter(config, custom_options)
        elif isinstance(config, S3WriterConfig):
            writer = S3Writer(config, process_date, custom_options)
        elif isinstance(config, IcebergWriterConfig):
            writer = IcebergWriter(config, process_date, custom_options)
        else:
            raise ValueError("Unsupported type of Writer")
        return writer
