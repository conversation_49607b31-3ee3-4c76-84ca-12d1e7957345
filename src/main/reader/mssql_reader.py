from reader.reader import Reader, JdbcConfig
from config.schemas.reader import MssqlReaderConfig
from typing import Optional, Dict
from utils.delta_bound_util import get_lowerbound, get_upperbound, QueryCondition
from pyspark.sql import SparkSession
import logging


class Mssql<PERSON><PERSON><PERSON>(Reader):
    def __init__(self, config: MssqlReaderConfig, options: Optional[Dict[str, str]], internal_endpoint_path: str, num_partitions: int = 1):
        if config.strategy == "delta":
            if config.delta_column is None:
                raise ValueError(
                    "deltaColumn cannot be empty when strategy is delta")
        self.endpoint = config.endpoint
        endpoint_config = config.endpoint_config
        cred = endpoint_config.authentication.credential  # type: ignore
        if options is None:
            options = {}
        options["user"] = cred.username
        options["password"] = cred.password
        self.jdbc_config = JdbcConfig("mssql", endpoint_config.system, endpoint_config.host, endpoint_config.port, endpoint_config.database,
                                      endpoint_config.schema_, config.table, cred.username, cred.password, options)         # type: ignore
        predicate_option = options.get("predicate", "Unknown")
        if predicate_option == "Unknown":
            if config.strategy == "delta":
                logging.info(
                    f"Get lowerbound for table {self.jdbc_config.system}.{self.jdbc_config.schema}.{self.jdbc_config.table}"
                )
                lowerbound = get_lowerbound(QueryCondition(
                    self.jdbc_config.system, self.jdbc_config.schema, self.jdbc_config.table), internal_endpoint_path)
                logging.info(
                    f"Get upperbound for table {self.jdbc_config.system}.{self.jdbc_config.schema}.{self.jdbc_config.table}"
                )
                upperbound = get_upperbound(
                    self.jdbc_config, config.delta_column)
                if lowerbound is None or upperbound is None:
                    self.predicate = "1=1"
                else:
                    self.predicate = f"""
                    {config.delta_column} >= CAST('{lowerbound}' AS DATETIME) AND 
                    {config.delta_column} < CAST('{upperbound}' AS DATETIME)
                """
                    self.lower_bound = lowerbound
                    self.upper_bound = upperbound
            else:
                self.predicate = "1=1"
        else:
            self.predicate = predicate_option
        self.num_partition = num_partitions
        options["driver"] = "com.microsoft.sqlserver.jdbc.SQLServerDriver"
        options["query"] = f"SELECT * FROM {self.jdbc_config.database}.{self.jdbc_config.schema}.{self.jdbc_config.table} WHERE {self.predicate}"
        options["url"] = f"jdbc:sqlserver://{self.jdbc_config.host}:{self.jdbc_config.port};encrypt=true;trustServerCertificate=true"
        self.options = options
        self.strategy = config.strategy

    def read(self, spark: SparkSession):
        df = spark.read \
            .format("jdbc") \
            .options(**self.options) \
            .load() \
            .repartition(self.num_partition)
        return df
