jobs:
  - name: job1
    readers:
      type: MysqlReader
      endpoint: mysql.endpoint.yaml
      table: customers
      strategy: delta
      delta_column: updated_at
      num_partitions: 1
    transformers:
       - type: ProjectionTransformer
         columns: 
           - id
           - name
    writers:
      type: PostgresWriter
      endpoint: postgresql.endpoint.yaml
      mode: append
      table: customers

  - name: job2
    readers:
      type: MysqlReader
      endpoint: mssql.endpoint.yaml
      table: customers
      strategy: delta
      delta_column: updated_at
      num_partitions: 1
    transformers:
      - type: WithColumnTransformer
        columns: 
          - range
          - CASE WHEN age between 20 and 40 THEN 1 ELSE 2 END
      - type: ProjectionTransformer
        columns: 
          - id
          - name
          - range
    writers:
      type: PostgresWriter
      endpoint: postgresql.endpoint.yaml
      mode: append
      table: customers

