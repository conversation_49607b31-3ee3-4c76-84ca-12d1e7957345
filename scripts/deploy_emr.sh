#!/bin/bash

# EMR Deployment Script
# Deploys the Spark pipeline to AWS EMR

set -e

# Configuration
EMR_CLUSTER_NAME="spark-metadata-pipeline"
EMR_RELEASE_LABEL="emr-6.15.0"
EC2_KEY_NAME="${EC2_KEY_NAME:-your-key-pair}"
SUBNET_ID="${SUBNET_ID:-subnet-xxxxxxxxx}"
S3_BUCKET="${S3_BUCKET_NAME:-your-data-bucket}"
S3_CODE_BUCKET="${S3_CODE_BUCKET:-your-code-bucket}"

echo "🚀 Deploying Spark Pipeline to EMR..."

# 1. Package and upload code to S3
echo "📦 Packaging code..."
tar -czf pipeline-code.tar.gz src/ scripts/
aws s3 cp pipeline-code.tar.gz s3://${S3_CODE_BUCKET}/pipeline/
rm pipeline-code.tar.gz

# 2. Create EMR cluster
echo "🏗️  Creating EMR cluster..."
CLUSTER_ID=$(aws emr create-cluster \
  --name "${EMR_CLUSTER_NAME}" \
  --release-label "${EMR_RELEASE_LABEL}" \
  --instance-groups '[
    {
      "Name": "Master",
      "Market": "ON_DEMAND",
      "InstanceRole": "MASTER",
      "InstanceType": "m5.xlarge",
      "InstanceCount": 1
    },
    {
      "Name": "Workers",
      "Market": "ON_DEMAND", 
      "InstanceRole": "CORE",
      "InstanceType": "m5.xlarge",
      "InstanceCount": 2
    }
  ]' \
  --applications Name=Spark Name=Hadoop \
  --ec2-attributes KeyName=${EC2_KEY_NAME},SubnetId=${SUBNET_ID} \
  --service-role EMR_DefaultRole \
  --job-flow-role EMR_EC2_DefaultRole \
  --log-uri s3://${S3_CODE_BUCKET}/emr-logs/ \
  --query 'ClusterId' \
  --output text)

echo "✅ EMR Cluster created: ${CLUSTER_ID}"

# 3. Wait for cluster to be ready
echo "⏳ Waiting for cluster to be ready..."
aws emr wait cluster-running --cluster-id ${CLUSTER_ID}

# 4. Add pipeline step
echo "🔧 Adding pipeline step..."
STEP_ID=$(aws emr add-steps \
  --cluster-id ${CLUSTER_ID} \
  --steps '[
    {
      "Name": "Setup Pipeline",
      "ActionOnFailure": "TERMINATE_CLUSTER",
      "HadoopJarStep": {
        "Jar": "command-runner.jar",
        "Args": [
          "bash", "-c",
          "aws s3 cp s3://'${S3_CODE_BUCKET}'/pipeline/pipeline-code.tar.gz . && tar -xzf pipeline-code.tar.gz"
        ]
      }
    },
    {
      "Name": "Run Pipeline",
      "ActionOnFailure": "CONTINUE",
      "HadoopJarStep": {
        "Jar": "command-runner.jar",
        "Args": [
          "spark-submit",
          "--deploy-mode", "cluster",
          "--master", "yarn",
          "src/main/pipeline_main.py",
          "--config_file", "job.emr.yaml",
          "--config_source_type", "local",
          "--config_root_dir", "src/main/config",
          "--app_type", "batch"
        ]
      }
    }
  ]' \
  --query 'StepIds[1]' \
  --output text)

echo "✅ Pipeline step added: ${STEP_ID}"
echo "🔍 Monitor progress: aws emr describe-step --cluster-id ${CLUSTER_ID} --step-id ${STEP_ID}"
echo "📊 EMR Console: https://console.aws.amazon.com/elasticmapreduce/home#cluster-details:${CLUSTER_ID}"

# 5. Set environment variables for the cluster
echo "🔧 Setting environment variables..."
cat > bootstrap.sh << 'EOF'
#!/bin/bash
export AWS_ENVIRONMENT=emr
export S3_BUCKET_NAME=${S3_BUCKET}
export MYSQL_HOST=${MYSQL_HOST}
export MYSQL_USERNAME=${MYSQL_USERNAME}
export MYSQL_PASSWORD=${MYSQL_PASSWORD}
export MSSQL_HOST=${MSSQL_HOST}
export MSSQL_USERNAME=${MSSQL_USERNAME}
export MSSQL_PASSWORD=${MSSQL_PASSWORD}
EOF

aws s3 cp bootstrap.sh s3://${S3_CODE_BUCKET}/bootstrap/
rm bootstrap.sh

echo "🎉 EMR deployment initiated!"
echo "Cluster ID: ${CLUSTER_ID}"
echo "Step ID: ${STEP_ID}"
