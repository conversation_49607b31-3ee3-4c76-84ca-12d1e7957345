# AWS EMR Setup Guide

This guide shows how to run the Spark pipeline on AWS EMR.

## 🔧 Prerequisites

1. **AWS CLI configured** with appropriate permissions
2. **S3 buckets** for code and data storage
3. **RDS instances** for MySQL and SQL Server (or equivalent)
4. **EMR service roles** configured
5. **EC2 key pair** for cluster access

## 🚀 Quick EMR Deployment

### 1. Set Environment Variables

```bash
# AWS Configuration
export AWS_DEFAULT_REGION="us-east-1"
export AWS_ENVIRONMENT="emr"

# S3 Configuration
export S3_BUCKET_NAME="your-data-lake-bucket"
export S3_CODE_BUCKET="your-code-bucket"

# EMR Configuration
export EC2_KEY_NAME="your-ec2-key-pair"
export SUBNET_ID="subnet-xxxxxxxxx"

# Database Configuration
export MYSQL_HOST="your-mysql-rds.region.rds.amazonaws.com"
export MYSQL_USERNAME="admin"
export MYSQL_PASSWORD="your-secure-password"
export MYSQL_DATABASE="production"
export MYSQL_SCHEMA="production"

export MSSQL_HOST="your-mssql-rds.region.rds.amazonaws.com"
export MSSQL_USERNAME="admin"
export MSSQL_PASSWORD="your-secure-password"
export MSSQL_DATABASE="production"
export MSSQL_SCHEMA="dbo"
```

### 2. Deploy to EMR

```bash
# Run the deployment script
./scripts/deploy_emr.sh
```

## 🔒 Security Configuration

### IAM Roles Required

1. **EMR Service Role** (`EMR_DefaultRole`)
2. **EMR EC2 Instance Profile** (`EMR_EC2_DefaultRole`)
3. **Custom execution role** with permissions for:
   - S3 read/write access
   - RDS connectivity
   - CloudWatch logging

### IAM Policy Example

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::your-data-bucket/*",
        "arn:aws:s3:::your-data-bucket"
      ]
    },
    {
      "Effect": "Allow",
      "Action": [
        "rds-db:connect"
      ],
      "Resource": [
        "arn:aws:rds-db:region:account:dbuser:db-instance/*"
      ]
    }
  ]
}
```

## 🏗️ Manual EMR Setup

### 1. Create EMR Cluster

```bash
aws emr create-cluster \
  --name "spark-metadata-pipeline" \
  --release-label "emr-6.15.0" \
  --instance-groups '[
    {
      "Name": "Master",
      "Market": "ON_DEMAND",
      "InstanceRole": "MASTER", 
      "InstanceType": "m5.xlarge",
      "InstanceCount": 1
    },
    {
      "Name": "Workers",
      "Market": "ON_DEMAND",
      "InstanceRole": "CORE",
      "InstanceType": "m5.xlarge", 
      "InstanceCount": 2
    }
  ]' \
  --applications Name=Spark Name=Hadoop \
  --ec2-attributes KeyName=your-key-pair,SubnetId=subnet-xxxxxxxxx \
  --service-role EMR_DefaultRole \
  --job-flow-role EMR_EC2_DefaultRole
```

### 2. Upload Code to S3

```bash
# Package code
tar -czf pipeline-code.tar.gz src/ scripts/

# Upload to S3
aws s3 cp pipeline-code.tar.gz s3://your-code-bucket/pipeline/
```

### 3. Submit Spark Job

```bash
aws emr add-steps \
  --cluster-id j-XXXXXXXXX \
  --steps '[
    {
      "Name": "Run Pipeline",
      "ActionOnFailure": "CONTINUE",
      "HadoopJarStep": {
        "Jar": "command-runner.jar",
        "Args": [
          "spark-submit",
          "--deploy-mode", "cluster",
          "--master", "yarn",
          "s3://your-code-bucket/pipeline/src/main/pipeline_main.py",
          "--config_file", "job.emr.yaml",
          "--config_source_type", "local",
          "--config_root_dir", "src/main/config",
          "--app_type", "batch"
        ]
      }
    }
  ]'
```

## 📊 Monitoring

### CloudWatch Logs
- EMR cluster logs: `s3://your-code-bucket/emr-logs/`
- Application logs: CloudWatch Logs

### EMR Console
- Monitor cluster status and steps
- View Spark UI through EMR console
- Check resource utilization

## 🔧 Configuration Files for EMR

The following files are configured for EMR deployment:

- `src/main/config/data/aws/aws.emr.yaml` - EMR AWS configuration
- `src/main/config/data/jobs/job.emr.yaml` - EMR job configuration  
- `src/main/config/data/endpoints/s3.prod.endpoint.yaml` - Production S3 endpoint
- `src/main/config/data/endpoints/mysql.prod.endpoint.yaml` - Production MySQL endpoint
- `src/main/config/data/endpoints/mssql.prod.endpoint.yaml` - Production SQL Server endpoint

## 🚨 Troubleshooting

### Common Issues

1. **IAM Permission Errors**
   - Ensure EMR roles have S3 and RDS permissions
   - Check VPC security groups allow database connections

2. **Database Connection Issues**
   - Verify RDS security groups allow EMR subnet access
   - Test database connectivity from EMR master node

3. **S3 Access Issues**
   - Confirm bucket policies allow EMR role access
   - Check bucket region matches EMR cluster region

### Debug Commands

```bash
# SSH to EMR master node
aws emr ssh --cluster-id j-XXXXXXXXX --key-pair-file your-key.pem

# Check Spark application logs
yarn logs -applicationId application_XXXXXXXXX_XXXX

# Monitor cluster status
aws emr describe-cluster --cluster-id j-XXXXXXXXX
```
