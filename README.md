# Spark Metadata-Driven Pipeline

A secure, configurable data pipeline that reads from MySQL/SQL Server databases and writes to S3 as Parquet files with proper AWS credential management.

## 🎯 What It Does

- **Reads**: MySQL and SQL Server customer data with incremental loading
- **Transforms**: Adds age_range column (young/mature) using Spark SQL
- **Writes**: Partitioned Parquet files to S3 with Snappy compression
- **Partitions**: By age_range for optimal query performance
- **Security**: Secure AWS credential management with environment-based configuration
- **Monitoring**: Job logging and progress tracking

## 🔒 Security Features

- **No hardcoded credentials** - AWS credentials separated from code
- **Environment-based configuration** - Different configs for dev/prod
- **IAM role support** - Recommended for production deployments
- **LocalStack support** - Safe development with dummy credentials

## 🚀 Quick Start


### Step 1: Environment Setup
```bash
# Create necessary directories
mkdir -p logs localstack

# Start all services (databases + LocalStack S3)
docker-compose up -d

# Wait for services to initialize (~30 seconds)
docker-compose ps  # Check all services are healthy
```

### Step 2: Database Setup
```bash
# Setup databases with sample customer data
./scripts/setup_databases.sh
```

### Step 3: Build Pipeline
```bash
# Build the Spark pipeline Docker image
docker build -t spark-pipeline .
```

### Step 4: Run the Pipeline

#### Option A: Direct Pipeline Execution (Recommended)
```bash
# Run the complete pipeline
docker run \
  -it \
  --rm \
  --name spark-env \
  --network spark-metadata-driven_spark-network \
  -e AWS_ENVIRONMENT=dev \
  -v "$(pwd)/src:/app/src" \
  -v "$(pwd)/logs:/app/logs" \
  spark-pipeline \
  python3 src/main/pipeline_main.py \
  --config_file job.iceberg.sample.yaml \
  --config_source_type local \
  --config_root_dir src/main/config \
  --app_type batch \
  --s3_type localstack \
  --s3_url http://localstack:4566 \
  --extra_jars_folder jars
```

#### Option B: Interactive Development
```bash
# Start interactive shell for development
docker run \
  -it \
  --rm \
  --name spark-env \
  --network spark-metadata-driven_spark-network \
  -e AWS_ENVIRONMENT=dev \
  -v "$(pwd)/src:/app/src" \
  -v "$(pwd)/logs:/app/logs" \
  spark-pipeline \
  bash

# Then run pipeline inside the container
python3 src/main/pipeline_main.py \
  --config_file job.iceberg.sample.yaml \
  --config_source_type local \
  --config_root_dir src/main/config \
  --app_type batch \
  --s3_type localstack \
  --s3_url http://localstack:4566 \
  --extra_jars_folder jars
```

### Step 5: Verify Results
```bash
# Check S3 output
docker exec -it localstack awslocal s3 ls s3://testbucket/ --recursive

# View pipeline logs
ls -la logs/
```

## 🔧 AWS Configuration

The pipeline uses secure, environment-based AWS configuration:

### Development (LocalStack)
```bash
# Uses dummy credentials from aws.dev.yaml
export AWS_ENVIRONMENT=dev
# No additional setup needed - uses LocalStack
```

### Production
```bash
# Set environment variables (NEVER put real credentials in files!)
export AWS_ENVIRONMENT=prod
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret-key"
export AWS_DEFAULT_REGION="us-east-1"
```

### Configuration Files
- `src/main/config/data/aws/aws.dev.yaml` - Development (safe dummy credentials)
- `src/main/config/data/aws/aws.prod.yaml` - Production template (uses env vars)
- See `src/main/config/data/aws/README.md` for detailed security guidelines

## 🛠️ Development Workflow

```bash
# View pipeline logs
ls -la logs/

# Stop all services
docker-compose down

# Rebuild after code changes
docker build -t spark-pipeline .

# Clean restart
docker-compose down -v && docker-compose up -d
```

## 🔍 Monitoring & Debugging

### Service Status
```bash
# Check all services
docker-compose ps

# View service logs
docker-compose logs postgres
docker-compose logs mysql
docker-compose logs sqlserver
docker-compose logs localstack

# Follow logs in real-time
docker-compose logs -f postgres
```

### Database Access
```bash
# PostgreSQL (job logging)
docker exec -it postgres psql -U postgres -d test

# MySQL (source data)
docker exec -it mysql mysql -u test -p123456 test

# SQL Server (source data)
docker exec -it sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P 123456aA
```

### Pipeline Monitoring
```bash
# Access Spark UI (when pipeline is running)
open http://localhost:4040

# View pipeline logs
tail -f logs/*.log

# Check S3 data
docker exec -it localstack awslocal s3 ls s3://testbucket/ --recursive
```

## 🚨 Troubleshooting

### Common Issues

#### AWS Credentials Error
```bash
# Error: Unable to load AWS credentials
# Solution: Check AWS_ENVIRONMENT is set
export AWS_ENVIRONMENT=dev

# For production, ensure environment variables are set
export AWS_ACCESS_KEY_ID="your-key"
export AWS_SECRET_ACCESS_KEY="your-secret"
```

#### Connection Refused to LocalStack
```bash
# Error: Connection refused to localhost:4566
# Solution: Use container name in Docker network
--s3_url http://localstack:4566  # Not localhost:4566
```

#### No Data Processed (record_count: 0)
```bash
# Pipeline uses incremental loading
# Clear job logger to force full reload
docker exec -it postgres psql -U postgres -d test -c "DELETE FROM job_logger;"
```

### Clean Reset
```bash
# Complete cleanup and restart
docker-compose down -v
docker rmi spark-pipeline
rm -rf logs localstack
mkdir -p logs localstack

# Rebuild everything
docker build -t spark-pipeline .
docker-compose up -d
./scripts/setup_databases.sh
```

### Performance Tuning
```bash
# Check Docker resources
docker system df
docker system prune

# Build with detailed logs
docker build -t spark-pipeline . --no-cache --progress=plain

# Increase Docker memory (8GB+ recommended)
# Docker Desktop → Settings → Resources → Memory
```

## 📁 Output Structure

Your data is saved as partitioned Parquet files in S3:

```
s3://testbucket/
├── mysql_customers/
│   ├── age_range=young/
│   │   └── part-*.snappy.parquet    # MySQL customers aged 20-40
│   └── age_range=mature/
│       └── part-*.snappy.parquet    # MySQL customers aged >40
└── mssql_customers/
    ├── age_range=young/
    │   └── part-*.snappy.parquet    # SQL Server customers aged 20-40
    └── age_range=mature/
        └── part-*.snappy.parquet    # SQL Server customers aged >40
```

**Features:**
- **Partition Field**: `age_range` (young: 20-40, mature: >40)
- **Compression**: Snappy for optimal performance
- **Format**: Parquet for analytics workloads
- **Metadata**: Includes `_loaded_at` timestamp

## ⚙️ Configuration

### Job Configuration
The pipeline uses YAML-based job configuration in `src/main/config/jobs/job.iceberg.sample.yaml`:

```yaml
jobs:
  - name: mysql_to_parquet
    readers:
      type: MysqlReader
      endpoint: mysql.endpoint.yaml
      table: customers
      incremental_column: updated_at  # For incremental loading
    transformers:
      - type: WithColumnTransformer
        columns:
          - colname: age_range
            expr: CASE WHEN age between 20 and 40 THEN 'young' ELSE 'mature' END
    writers:
      type: IcebergWriter
      endpoint: s3.localstack.endpoint.yaml
      s3_path: mysql_customers
      partition_columns:
        - age_range

  - name: mssql_to_parquet
    readers:
      type: MssqlReader
      endpoint: mssql.endpoint.yaml
      table: customers
      incremental_column: updated_at
    transformers:
      - type: WithColumnTransformer
        columns:
          - colname: age_range
            expr: CASE WHEN age between 20 and 40 THEN 'young' ELSE 'mature' END
    writers:
      type: IcebergWriter
      endpoint: s3.localstack.endpoint.yaml
      s3_path: mssql_customers
      partition_columns:
        - age_range
```

### Endpoint Configuration
Database and S3 endpoints are configured separately:
- `src/main/config/data/endpoints/mysql.endpoint.yaml`
- `src/main/config/data/endpoints/mssql.endpoint.yaml`
- `src/main/config/data/endpoints/s3.localstack.endpoint.yaml`


## 📊 Data Flow

```mermaid
graph LR
    A[MySQL Database] --> D[Spark Pipeline]
    B[SQL Server Database] --> D
    C[PostgreSQL Job Logger] --> D
    D --> E[Transform & Partition]
    E --> F[S3 Parquet Files]

    D --> G[Job Logging]
    G --> C
```

### Pipeline Steps
1. **Read** → Extract data from MySQL/SQL Server with incremental loading
2. **Transform** → Add age_range column using Spark SQL
3. **Partition** → Group by age_range for optimal performance
4. **Write** → Save as compressed Parquet files to S3
5. **Log** → Track job progress and timestamps

## ✅ Key Features

- **🔒 Secure**: No hardcoded credentials, environment-based AWS config
- **📈 Scalable**: Spark-based processing with partitioning
- **🔄 Incremental**: Only processes new/updated records
- **📊 Monitored**: Job logging and progress tracking
- **🗜️ Optimized**: Snappy compression and Parquet format
- **🐳 Containerized**: Docker-based deployment
- **🧪 Testable**: LocalStack for development

**Perfect for**: Secure MySQL/SQL Server → S3 data lake migrations

## 📋 Sample Data

The `setup_databases.sh` script creates identical sample data in both MySQL and SQL Server:

### Source Data (customers table)
```sql
+----+---------------+-----+---------------------+
| id | name          | age | updated_at          |
+----+---------------+-----+---------------------+
|  1 | John Doe      |  25 | 2025-01-03 12:00:00 |
|  2 | Jane Smith    |  35 | 2025-01-03 12:01:00 |
|  3 | Bob Johnson   |  45 | 2025-01-03 12:02:00 |
|  4 | Alice Brown   |  28 | 2025-01-03 12:03:00 |
|  5 | Charlie Wilson|  52 | 2025-01-03 12:04:00 |
+----+---------------+-----+---------------------+
```

### After Pipeline Processing
The pipeline adds the `age_range` column and `_loaded_at` timestamp:

**Partition: age_range=young** (ages 20-40)
- John Doe (25), Jane Smith (35), Alice Brown (28)

**Partition: age_range=mature** (ages >40)
- Bob Johnson (45), Charlie Wilson (52)

### Job Logging
Progress is tracked in PostgreSQL `job_logger` table:
```sql
SELECT * FROM job_logger ORDER BY start_time DESC;
```

## 🏗️ Architecture

- **Spark**: Distributed data processing engine
- **Docker**: Containerized deployment
- **LocalStack**: AWS S3 simulation for development
- **PostgreSQL**: Job logging and metadata
- **MySQL/SQL Server**: Source databases
- **Parquet**: Columnar storage format
- **S3**: Object storage (AWS or LocalStack)
