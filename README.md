# Database to Parquet Pipeline

Read data from MySQL/SQL Server databases and write to S3 as Parquet files.

## 🎯 What It Does

- **Reads**: MySQL and SQL Server customer data
- **Transforms**: Adds age_range column (young/mature)
- **Writes**: Parquet files to S3 with Snappy compression
- **Partitions**: By age_range for better performance


### Step-by-Step Setup

```bash
# 1. Create necessary directories
mkdir -p logs localstack
```

```bash
# 2. Start database services
docker-compose up -d
```

```bash
# 3. Wait for databases to initialize (about 30 seconds)
# Check status: docker-compose ps
```

```bash
# 4. Setup databases with sample data (optional)
./scripts/setup_databases.sh
```

```bash
# 5. Build the pipeline Docker image
docker build -t spark-pipeline .
```

```bash
# 6. Run python env
docker run \
  -it \
  --rm \
  --name spark-env \
  --network spark-metadata-driven_spark-network \
  -v "$(pwd)/src:/app/src" \
  -v "$(pwd)/logs:/app/logs" \
  spark-pipeline \
  bash

```

```bash
# 7. Run the pipeline in docker python env
python3 src/main/pipeline_main.py \
  --config_file job.iceberg.sample.yaml \
  --config_source_type local \
  --config_root_dir src/main/config \
  --app_type batch \
  --s3_type localstack \
  --s3_url http://localhost:4566 \
  --extra_jars_folder jars
```



### Development Workflow

# View logs
ls -la logs/

# Stop services when done
docker-compose down

# Rebuild image after code changes
docker build -t spark-pipeline .
```

### Useful Developer Commands

```bash
# Check service status
docker-compose ps

# View service logs
docker-compose logs postgres
docker-compose logs mysql
docker-compose logs sqlserver
docker-compose logs localstack

# Access database directly
docker exec -it postgres psql -U postgres -d test
docker exec -it mysql mysql -u test -p123456 test
docker exec -it sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P 123456aA


# Access Spark UI (when pipeline is running)
open http://localhost:4040
```

### Troubleshooting

```bash
# Clean up everything and start fresh
docker-compose down -v
docker rmi spark-pipeline
rm -rf logs localstack
# Then repeat setup steps

# Check Docker resources
docker system df
docker system prune

# View detailed build logs
docker build -t spark-pipeline . --no-cache --progress=plain
```
```

## 📁 Output Structure

Your data is saved as Parquet files in S3:

```
s3://mock-bucket/
├── mysql_customers/
│   └── age_range=young/
│       └── part-*.snappy.parquet    # MySQL customer data
└── mssql_customers/
    └── age_range=mature/
        └── part-*.snappy.parquet    # SQL Server customer data
```

**Partition Field**: `age_range` (young/mature based on age 20-40)

## ⚙️ Configuration

Simple job configuration in `src/main/resources/jobs/job.iceberg.sample.yaml`:

```yaml
jobs:
  - name: mysql_to_parquet
    readers:
      type: MysqlReader
      endpoint: mysql.endpoint.yaml
      table: customers
    transformers:
      - type: WithColumnTransformer
        columns:
          - colname: age_range
            expr: CASE WHEN age between 20 and 40 THEN 'young' ELSE 'mature' END
    writers:
      type: IcebergWriter
      endpoint: s3.localstack.endpoint.yaml
      s3_path: mysql_customers
      partition_columns:
        - age_range
```


## 📊 Data Flow

1. **MySQL/SQL Server** → Read customer data
2. **Transform** → Add age_range column (young: 20-40, mature: >40)
3. **Partition** → Group by age_range
4. **Write** → Save as Parquet files to S3


## ✅ What You Get

- **Clean Parquet files** with Snappy compression
- **Partitioned data** for better query performance  
- **Simple S3 structure** - easy to understand
- **No complex metadata** - just pure Parquet files
- **Automated scripts** - easy to run and verify

Perfect for: MySQL/SQL Server → S3 Parquet data migration

## 📋 Sample Data

The `setup_databases.sh` script creates this sample data:

**MySQL & SQL Server customers table:**
```
+----+---------------+-----+---------------------+
| id | name          | age | updated_at          |
+----+---------------+-----+---------------------+
|  1 | John Doe      |  25 | 2025-01-03 12:00:00 |
|  2 | Jane Smith    |  35 | 2025-01-03 12:00:00 |
|  3 | Bob Johnson   |  45 | 2025-01-03 12:00:00 |
|  4 | Alice Brown   |  28 | 2025-01-03 12:00:00 |
|  5 | Charlie Wilson|  52 | 2025-01-03 12:00:00 |
+----+---------------+-----+---------------------+
```

**After pipeline transformation:**
- Ages 20-40 → `age_range = 'young'` (John, Jane, Alice)
- Ages >40 → `age_range = 'mature'` (Bob, Charlie)
